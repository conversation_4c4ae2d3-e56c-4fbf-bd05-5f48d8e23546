import React, { useState, useMemo } from 'react';
import emailIcon from '@/assets/images/icon-email.svg';
import verifyIcon from '@/assets/images/icon-verify.svg';
import {
  Form,
  Button,
  Modal,
  Input,
  message,
  type InputRef,
  ConfigProvider,
} from 'antd';
import { api } from '@/services';
import EditButton from './coms/EditButton';
import {
  ArrowLeftOutlined,
  EditFilled,
  CheckOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import ImageUploadWithCrop from '@/components/ImageUploadWithCrop';
import { useLanguage } from '@/hooks/useLanguage';
import { getDisplayAddress } from '@/utils/utils';
import type { UserProfileResponse, UpdateProfileRequest } from '@/types/api';
import { useAuthStore } from '@/store/authStore';
import FormButton from '@/pages/Register/coms/FormButton';
import EditProfilePassword from './coms/EditProfilePassword';
import CountrySelector from '@/components/CountryStateSelector/CountrySelector';
import StateSelector from '@/components/CountryStateSelector/StateSelector';

interface ProfileProps {
  visible: boolean;
  onClose: () => void;
}
const { TextArea } = Input;

const Profile: React.FC<ProfileProps> = ({ visible, onClose }) => {
  const { t, isEnUS } = useLanguage();
  const { isArtist } = useAuthStore.getState();

  // 编辑状态管理
  const [editingFields, setEditingFields] = useState<Record<string, boolean>>(
    {}
  );
  const [showPasswordForm, setShowPasswordForm] = useState(false);

  // 在组件顶层声明所有的 form 实例，避免 hooks 规则违反
  const [aliasForm] = Form.useForm();
  const [nameForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [addressForm] = Form.useForm();
  const [stageNameForm] = Form.useForm();
  const [bioForm] = Form.useForm();

  // 开始编辑
  const startEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: true }));
  };
  // 取消编辑
  const cancelEdit = (fieldName: string) => {
    setEditingFields(prev => ({ ...prev, [fieldName]: false }));
  };
  // 保存编辑
  const saveEdit = async ({
    fieldName,
    requestData,
  }: {
    fieldName: string;
    requestData: UpdateProfileRequest;
  }) => {
    try {
      // 直接调用API保存
      await api.user.updateProfile(requestData);
      setEditingFields(prev => ({ ...prev, [fieldName]: false }));
      message.success(t('common.saveSuccess'));
    } catch (error) {
      message.error(t('common.saveFailed'));
    }
  };
  // 模拟用户信息
  const userInfo: UserProfileResponse = {
    email: '<EMAIL>',
    alias: 'zhang san',
    firstName: '三',
    lastName: '张',
    addressLine1:
      '桥西区春风路100号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号号',
    addressLine2: '石家庄市',
    stateProvince: '河北省',
    countryCode: 'CN',
    postalZipCode: '100000',
    avatarUrl:
      'https://zos.alipayobjects.com/rmsportal/jkjgkEfvpUPVyRjUImniVslZfWPnJuuZ.png',
    stageName: '',
    bio: '',
    displayName: '张三',
    accountId: '**********',
    mobile: '**********',
  };
  const displayAddress = useMemo(() => {
    return getDisplayAddress(userInfo);
  }, [userInfo]);

  // 头像上传成功处理
  const handleAvatarUploadSuccess = (url: string) => {
    // 这里可以调用API保存头像URL到后端
    console.log('头像上传成功:', url);
  };

  // 自定义上传请求
  const customAvatarUpload = (options: any) => {
    const { file, onSuccess, onError } = options;

    // 模拟上传过程
    setTimeout(() => {
      if (Math.random() > 0.1) {
        // 90% 成功率
        const mockUrl = URL.createObjectURL(file);
        onSuccess({ url: mockUrl });
      } else {
        onError(new Error('上传失败'));
      }
    }, 1000);
  };
  interface ProfileItem {
    label: string;
    value: string;
    fieldName: string;
  }

  // 生成显示模式的组件
  const generateProfileItem = (item: ProfileItem) => {
    return (
      <div className="flex items-start" key={item.fieldName}>
        <span className="text-label mr-6px w-100px text-right -ml-100px">
          {item.label}:
        </span>
        <div className="flex-1">
          <span className="text-white">{item.value}</span>
          <EditFilled
            className="ml-10px cursor-pointer hover:text-primary"
            onClick={() => startEdit(item.fieldName)}
          />
        </div>
      </div>
    );
  };

  // 生成普通输入框编辑模式的组件 (input\TextArea)
  const generateInputEditItem = (
    type: 'input' | 'textarea',
    item: ProfileItem,
    form: any
  ) => {
    const handleSave = async (values: any) => {
      saveEdit({
        fieldName: item.fieldName,
        requestData: {
          [item.fieldName]: values[item.fieldName] || '',
        },
      });
    };

    return (
      <div className="flex items-start -ml-106px" key={item.fieldName}>
        <div className="flex-1">
          <Form
            form={form}
            requiredMark={false}
            onFinish={handleSave}
            autoComplete="off"
          >
            <Form.Item
              label={
                <span className="text-label text-12px w-100px">
                  {item.label}
                </span>
              }
              name={item.fieldName}
            >
              {type === 'input' ? (
                <Input
                  defaultValue={item.value}
                  className="s-profile-input"
                  size="large"
                />
              ) : (
                <TextArea
                  rows={4}
                  defaultValue={item.value}
                  className="s-profile-input"
                />
              )}
            </Form.Item>
            <EditButton
              className="pl-114px"
              htmlType="submit"
              onCancel={() => cancelEdit(item.fieldName)}
            />
          </Form>
        </div>
      </div>
    );
  };

  // 姓名编辑的特殊组件（两个输入框）
  const generateNameEditItem = (item: ProfileItem, form: any) => {
    const handleNameSave = async (values: any) => {
      console.log('values----', values);
      saveEdit({
        fieldName: item.fieldName,
        requestData: {
          firstName: values.firstName || '',
          lastName: values.lastName || '',
        },
      });
    };
    const generateNameFormItem = () => {
      const rules = [
        {
          min: 2,
          message: t('auth.register.step3.validation.nameLength', {
            min: 2,
            max: 12,
          }),
        },
        {
          max: 12,
          message: t('auth.register.step3.validation.nameLength', {
            min: 2,
            max: 12,
          }),
        },
        {
          pattern: /^[a-zA-Z\u4e00-\u9fa5\s·'-]+$/,
          message: t('auth.register.step3.validation.namePattern'), // '姓名只能包含中英文、空格和常用符号'
        },
      ];
      const nameFormItem = (
        <Form.Item
          label={
            <span className="text-label text-12px w-100px">
              {t('auth.register.step3.form.firstName')}
            </span>
          }
          name="firstName"
          key="firstName"
          rules={rules}
        >
          <Input
            placeholder={t('auth.register.step3.form.firstNamePlaceholder')}
            className="s-profile-input"
            defaultValue={userInfo.firstName}
            size="large"
          />
        </Form.Item>
      );
      const lastNameFormItem = (
        <Form.Item
          label={
            <span className="text-label text-12px  w-100px ">
              {t('auth.register.step3.form.lastName')}
            </span>
          }
          name="lastName"
          key="lastName"
          rules={rules}
        >
          <Input
            placeholder={t('auth.register.step3.form.lastNamePlaceholder')}
            className="s-profile-input"
            defaultValue={userInfo.lastName}
            size="large"
          />
        </Form.Item>
      );
      return isEnUS
        ? [nameFormItem, lastNameFormItem]
        : [lastNameFormItem, nameFormItem];
    };

    return (
      <div className="flex items-start  -ml-106px" key={item.fieldName}>
        <div className="flex-1">
          <Form
            form={form}
            requiredMark={false}
            onFinish={handleNameSave}
            autoComplete="off"
          >
            {generateNameFormItem()}

            <EditButton
              className="pl-114px"
              htmlType="submit"
              onCancel={() => cancelEdit(item.fieldName)}
            />
          </Form>
        </div>
      </div>
    );
  };
  // 地址编辑的特殊组件
  const generateAddressEditItem = (item: ProfileItem, form: any) => {
    const handleAddressSave = async (values: any) => {
      console.log('values----', values);
      saveEdit({
        fieldName: item.fieldName,
        requestData: {
          addressLine1: values.address || '',
          addressLine2: values.city || '', // city
          countryCode: values.country || '',
          stateProvince: values.state || '',
          postalZipCode: values.postalZipCode || '',
        },
      });
    };
    return (
      <div className="flex items-start -ml-106px " key={item.fieldName}>
        <div className="flex-1">
          <Form
            form={form}
            requiredMark={false}
            onFinish={handleAddressSave}
            autoComplete="off"
          >
            <Form.Item
              label={
                <span className="text-label text-12px  w-100px ">
                  {t('auth.register.step3.form.address')}
                </span>
              }
              name="address"
            >
              <Input
                defaultValue={userInfo.addressLine1}
                placeholder={t('auth.register.step3.form.addressPlaceholder')}
                className="s-profile-input"
                size="large"
              />
            </Form.Item>
            <Form.Item
              label={
                <span className="text-label text-12px  w-100px ">
                  {t('auth.register.step3.form.city')}
                </span>
              }
              name="city"
            >
              <Input
                defaultValue={userInfo.addressLine2}
                placeholder={t('auth.register.step3.form.cityPlaceholder')}
                className="s-profile-input"
                size="large"
              />
            </Form.Item>

            <Form.Item
              label={
                <span className="text-label text-12px  w-100px ">
                  {t('auth.register.step3.form.country')}
                </span>
              }
              name="country"
            >
              <CountrySelector
                placeholder={t('common.form.selectCountry')}
                size="small"
                value={userInfo.countryCode}
                className="s-profile-selector !h-35px"
              />
            </Form.Item>

            <Form.Item
              label={
                <span className="text-label text-12px  w-100px ">
                  {t('common.state')}
                </span>
              }
              name="state"
            >
              <StateSelector
                form={form}
                placeholder={t('common.form.selectState')}
                size="small"
                value={userInfo.stateProvince}
                className="s-profile-selector !h-35px"
              />
            </Form.Item>

            <Form.Item
              label={
                <span className="text-label text-12px  w-100px ">
                  {t('auth.register.step3.form.postalZipCode')}
                </span>
              }
              name="postalZipCode"
            >
              <Input
                placeholder={t(
                  'auth.register.step3.form.postalZipCodePlaceholder'
                )}
                defaultValue={userInfo.postalZipCode}
                className="s-profile-input"
                size="large"
              />
            </Form.Item>
            <EditButton
              className="pl-114px"
              onCancel={() => cancelEdit(item.fieldName)}
            />
          </Form>
        </div>
      </div>
    );
  };
  // 生成邮箱编辑的特殊组件
  const generateEmailEditItem = (item: ProfileItem, form: any) => {
    const handleEmailSave = async (values: any) => {
      console.log('values----', values);
    };
    return (
      <div className="flex items-start -ml-106px " key={item.fieldName}>
        <div className="flex-1">
          <Form
            form={form}
            requiredMark={false}
            onFinish={handleEmailSave}
            autoComplete="off"
          >
            <Form.Item
              label={
                <span className="text-label text-12px  w-100px ">
                  {t('common.email')}
                </span>
              }
              name="email"
            >
              <Input
                placeholder={t('common.form.emailRequired')}
                className="s-profile-input"
                size="large"
                defaultValue={userInfo.email}
              />
            </Form.Item>
            <Form.Item
              label={
                <span className="text-label text-12px  w-100px ">
                  {t('common.emailCode')}
                </span>
              }
              name="verificationCode"
            >
              <Input
                placeholder={t('auth.login.form.emailCodeLabel')}
                className="s-profile-input"
                size="large"
              />
            </Form.Item>
            <div className={`pl-114px flex flex-col gap-12px`}>
              <div>
                <Button
                  htmlType="button"
                  size="small"
                  type="primary"
                  onClick={() => {}}
                  className="leading-24px rounded-2px text-12px text-white hover:!text-white"
                >
                  <img src={emailIcon} className="w-13px" alt="sendCode" />
                  {t('common.verificationCode')}
                </Button>
              </div>
              <div className="flex gap-12px">
                <Button
                  htmlType="button"
                  size="small"
                  type="primary"
                  ghost
                  onClick={() => {
                    console.log('cancel');
                    cancelEdit(item.fieldName);
                  }}
                  className="leading-24px rounded-2px text-12px"
                >
                  {t('common.cancel')}
                </Button>
                <Button
                  htmlType="button"
                  size="small"
                  type="primary"
                  className="text-white hover:!text-white leading-24px text-12px  rounded-2px"
                  onClick={() => {}}
                  icon={<CheckOutlined className="text-white" />}
                >
                  {/* <CheckOutlined className="text-white" /> */}
                  {t('common.verify')}
                </Button>

                {/* <Button
                  htmlType="button"
                  size="small"
                  type="primary"
                  className="text-white hover:!text-white leading-24px rounded-2px"
                  onClick={() => {}}
                >
                  <img src={emailIcon} className="w-13px" alt="resend" />
                  {t('common.resendCode')}
                </Button> */}
              </div>
            </div>
          </Form>
        </div>
      </div>
    );
  };
  /**
   * 使用 TypeScript 的 as const 和 typeof 语法，将数组直接转换为枚举类型
   */
  const profileNameList = [
    'alias',
    'name',
    'email',
    'address',
    'stageName',
    'bio',
  ] as const;
  type ProfileName = (typeof profileNameList)[number];

  interface ProfileListItem {
    name: ProfileName;
    editable: boolean;
    _id?: number;
    showElement: React.ReactNode;
    editElement?: React.ReactNode;
  }
  const renderProfileInfo = () => {
    let profileList: ProfileListItem[] = [
      {
        name: 'alias',
        editable: !!editingFields['alias'],
        showElement: generateProfileItem({
          label: t('common.alias'),
          value: userInfo.alias,
          fieldName: 'alias',
        }),
        editElement: generateInputEditItem(
          'input',
          {
            label: t('common.alias'),
            value: userInfo.alias,
            fieldName: 'alias',
          },
          aliasForm
        ),
      },
      {
        name: 'name',
        editable: !!editingFields['name'],
        showElement: generateProfileItem({
          label: t('common.name'),
          value: userInfo.displayName,
          fieldName: 'name',
        }),
        editElement: generateNameEditItem(
          {
            label: t('common.name'),
            value: userInfo.displayName,
            fieldName: 'name',
          },
          nameForm
        ),
      },
      {
        name: 'email',
        editable: !!editingFields['email'],
        showElement: generateProfileItem({
          label: t('common.email'),
          value: userInfo.email,
          fieldName: 'email',
        }),
        editElement: generateEmailEditItem(
          {
            label: t('common.email'),
            value: userInfo.email,
            fieldName: 'email',
          },
          emailForm
        ),
      },
      {
        name: 'address',
        editable: !!editingFields['address'],
        showElement: generateProfileItem({
          label: t('common.address'),
          value: displayAddress,
          fieldName: 'address',
        }),
        editElement: generateAddressEditItem(
          {
            label: t('common.address'),
            value: displayAddress,
            fieldName: 'address',
          },
          addressForm
        ),
      },
    ];
    const artistProfileList: ProfileListItem[] = [
      {
        name: 'stageName',
        editable: !!editingFields['stageName'],
        showElement: generateProfileItem({
          label: t('common.stageName'),
          value: userInfo.stageName,
          fieldName: 'stageName',
        }),
        editElement: generateInputEditItem(
          'input',
          {
            label: t('common.stageName'),
            value: userInfo.stageName,
            fieldName: 'stageName',
          },
          stageNameForm
        ),
      },
      {
        name: 'bio',
        editable: !!editingFields['bio'],
        showElement: generateProfileItem({
          label: t('common.artistBio'),
          value: userInfo.bio,
          fieldName: 'bio',
        }),
        editElement: generateInputEditItem(
          'textarea',
          {
            label: t('common.artistBio'),
            value: userInfo.bio,
            fieldName: 'bio',
          },
          bioForm
        ),
      },
    ];

    if (isArtist) {
      profileList = profileList.concat(artistProfileList);
    }
    profileList.forEach((item, index) => {
      item._id = index;
    });

    return (
      <div className="flex flex-col gap-15px">
        {profileList.map(item => {
          return item.editable ? item.editElement : item.showElement;
        })}
      </div>
    );
  };

  if (!visible) return null;
  return (
    <ConfigProvider
      theme={{
        components: {
          Select: {
            optionLineHeight: '35px',
            optionHeight: 35,
          },
          Form: {
            fontSize: 12,
          },
        },
      }}
    >
      <Modal
        open={visible}
        onCancel={onClose}
        width={1000}
        className="
      [&.ant-modal_.ant-modal-close]:(inset-ie-auto ml-12px top-40px)
      [&.ant-modal_.ant-modal-content]:(px-50px pt-35px pb-112px) "
        footer={null}
        keyboard={false}
        maskClosable={false}
        centered
        closeIcon={
          <div className="flex items-center gap-2 hover:opacity-45">
            <ArrowLeftOutlined
              style={{ fontSize: '16px', color: 'var(--color-label)' }}
            />
            <span className="text-#B5B5B5 font-400">Back</span>
          </div>
        }
        title={
          <div className=" text-center text-white text-32px font-700">
            Profile
          </div>
        }
      >
        <div className="flex flex-col items-center pt-55px w-500px mx-auto ">
          {/* 头像区域 */}
          <div className="flex flex-col items-center mb-30px">
            <ImageUploadWithCrop
              avatarUrl={userInfo.avatarUrl}
              avatarSize={230}
              userName={userInfo.alias}
              onUploadSuccess={handleAvatarUploadSuccess}
              customRequest={customAvatarUpload}
            />
          </div>

          {/* 用户信息区域 */}
          <div className="w-full text-12px">
            <div className="text-center text-white text-22px font-medium mb-30px">
              {userInfo.displayName}
            </div>
            <div>{renderProfileInfo()}</div>
            {/* 密码表单区域 */}
            <EditProfilePassword
              visible={showPasswordForm}
              onClose={() => setShowPasswordForm(false)}
            />
            <div className="mt-80px flex justify-center gap-20px">
              {!showPasswordForm && (
                <FormButton
                  ghost
                  variant="outlined"
                  className="flex-1"
                  onClick={() => setShowPasswordForm(true)}
                >
                  {t('common.changePassword')}
                </FormButton>
              )}
              <FormButton type="primary" className="flex-1">
                {t('common.navigation.logout')}
              </FormButton>
            </div>
          </div>
        </div>
      </Modal>
    </ConfigProvider>
  );
};

export default Profile;
